# SWIM联邦学习算法主程序
# 实现了基于对比学习的联邦学习方法SWIM (Selective Weight Importance Matching)

import json
import torch.optim as optim
import argparse
import copy
import datetime
from utils import *


def get_args():
    """
    解析命令行参数

    Returns:
        args: 包含所有配置参数的命名空间对象
    """
    parser = argparse.ArgumentParser()
    # 模型相关参数
    parser.add_argument('--model', type=str, default='resnet50', help='训练中使用的神经网络模型')
    parser.add_argument('--dataset', type=str, default='cifar10', help='训练使用的数据集')
    parser.add_argument('--net_config', type=lambda x: list(map(int, x.split(', '))))  # 网络配置参数

    # 数据分布相关参数
    parser.add_argument('--partition', type=str, default='noniid', help='数据分割策略')
    parser.add_argument('--batch-size', type=int, default=64, help='训练批次大小 (默认: 64)')
    parser.add_argument('--beta', type=float, default=0.5,
                        help='数据分割时狄利克雷分布的参数')

    # 训练相关参数
    parser.add_argument('--lr', type=float, default=0.1, help='学习率 (默认: 0.1)')
    parser.add_argument('--epochs', type=int, default=10, help='本地训练轮数')
    parser.add_argument('--optimizer', type=str, default='sgd', help='优化器类型')
    parser.add_argument('--reg', type=float, default=1e-5, help="L2正则化强度")
    parser.add_argument('--dropout_p', type=float, required=False, default=0.0, help="Dropout概率. 默认=0.0")

    # 联邦学习相关参数
    parser.add_argument('--n_parties', type=int, default=10, help='分布式集群中的客户端数量')
    parser.add_argument('--alg', type=str, default='swim',
                        help='通信策略: swim')
    parser.add_argument('--comm_round', type=int, default=100, help='最大通信轮数')
    parser.add_argument('--sample_fraction', type=float, default=1.0, help='每轮采样的客户端比例')
    parser.add_argument('--server_momentum', type=float, default=0, help='服务器动量 (FedAvgM)')

    # SWIM算法特定参数
    parser.add_argument('--kr', type=float, default=0.4, help='选择k的比例 (默认: 0.4)')
    parser.add_argument('--mu', type=float, default=1, help='fedprox或swim的mu参数')
    parser.add_argument('--out_dim', type=int, default=256, help='投影层的输出维度')
    parser.add_argument('--temperature', type=float, default=0.5, help='对比损失的温度参数')
    parser.add_argument('--model_buffer_size', type=int, default=1,
                        help='为对比损失存储多少个先前模型')
    parser.add_argument('--pool_option', type=str, default='FIFO', help='FIFO或BOX')
    parser.add_argument('--use_project_head', type=int, default=1)  # 是否使用投影头

    # 系统相关参数
    parser.add_argument('--init_seed', type=int, default=0, help="随机种子")
    parser.add_argument('--device', type=str, default='cuda:0', help='运行程序的设备')
    parser.add_argument('--datadir', type=str, required=False, default="./data/", help="数据目录")
    parser.add_argument('--logdir', type=str, required=False, default="./logs/", help='日志目录路径')
    parser.add_argument('--modeldir', type=str, required=False, default="./models/", help='模型目录路径')
    parser.add_argument('--log_file_name', type=str, default=None, help='日志文件名')

    # 模型加载和保存相关参数
    parser.add_argument('--load_model_file', type=str, default=None, help='作为全局模型加载的模型')
    parser.add_argument('--load_pool_file', type=str, default=None, help='要加载的旧模型池路径')
    parser.add_argument('--load_model_round', type=int, default=None,
                        help='已加载模型执行了多少轮')
    parser.add_argument('--load_first_net', type=int, default=1, help='是否将第一个网络作为旧网络加载')
    parser.add_argument('--save_model', type=int, default=0)  # 是否保存模型

    # 其他参数
    parser.add_argument('--normal_model', type=int, default=0, help='使用普通模型还是聚合模型')
    parser.add_argument('--loss', type=str, default='contrastive')  # 损失函数类型
    parser.add_argument('--local_max_epoch', type=int, default=10,
                        help='本地最优训练的轮数')

    args = parser.parse_args()
    return args


def init_nets(net_configs, n_parties, args, device='cpu'):
    """
    初始化网络模型

    Args:
        net_configs: 网络配置参数
        n_parties: 客户端数量
        args: 命令行参数
        device: 运行设备

    Returns:
        nets: 包含所有客户端网络的字典
        model_meta_data: 模型元数据（各层形状）
        layer_type: 层类型列表
    """
    # 初始化网络字典，为每个客户端分配一个网络
    nets = {net_i: None for net_i in range(n_parties)}

    # 根据数据集确定分类数量
    if args.dataset in {'mnist', 'cifar10', 'svhn', 'fmnist'}:
        n_classes = 10
    elif args.dataset == 'celeba':
        n_classes = 2
    elif args.dataset == 'cifar100':
        n_classes = 100
    elif args.dataset == 'tinyimagenet':
        n_classes = 200
    elif args.dataset == 'femnist':
        n_classes = 26
    elif args.dataset == 'emnist':
        n_classes = 47
    elif args.dataset == 'xray':
        n_classes = 2

    # 根据模型类型创建网络
    if args.normal_model:
        # 使用普通模型（不带对比学习头）
        for net_i in range(n_parties):
            if args.model == 'simple-cnn':
                net = SimpleCNNMNIST(input_dim=(16 * 4 * 4), hidden_dims=[120, 84], output_dim=10)
            # 将模型移动到指定设备
            if device == 'cpu':
                net.to(device)
            else:
                net = net.cuda()
            nets[net_i] = net
    else:
        # 使用联邦对比学习模型
        for net_i in range(n_parties):
            if args.use_project_head:
                # 使用带投影头的模型
                net = ModelFedCon(args.model, args.out_dim, n_classes, net_configs)
            else:
                # 使用不带投影头的模型
                net = ModelFedCon_noheader(args.model, args.out_dim, n_classes, net_configs)
            # 将模型移动到指定设备
            if device == 'cpu':
                net.to(device)
            else:
                net = net.cuda()
            nets[net_i] = net

    # 提取模型元数据（用于后续的模型聚合）
    model_meta_data = []
    layer_type = []
    for (k, v) in nets[0].state_dict().items():
        model_meta_data.append(v.shape)  # 记录每层的形状
        layer_type.append(k)  # 记录层的名称

    return nets, model_meta_data, layer_type


def train_net(net_id, net, train_dataloader, test_dataloader, epochs, lr, args_optimizer, args, device="cpu"):
    """
    标准的网络训练函数（不使用SWIM算法）

    Args:
        net_id: 网络ID
        net: 要训练的神经网络
        train_dataloader: 训练数据加载器
        test_dataloader: 测试数据加载器
        epochs: 训练轮数
        lr: 学习率
        args_optimizer: 优化器类型
        args: 其他参数
        device: 运行设备

    Returns:
        train_acc: 训练准确率
        test_acc: 测试准确率
    """
    # 使用数据并行包装网络
    net = nn.DataParallel(net)
    net.cuda()
    logger.info('Training network %s' % str(net_id))
    logger.info('n_training: %d' % len(train_dataloader))
    logger.info('n_test: %d' % len(test_dataloader))

    # 计算训练前的准确率
    train_acc, _ = compute_accuracy(net, train_dataloader, device=device)
    test_acc, conf_matrix, _ = compute_accuracy(net, test_dataloader, get_confusion_matrix=True, device=device)

    logger.info('>> Pre-Training Training accuracy: {}'.format(train_acc))
    logger.info('>> Pre-Training Test accuracy: {}'.format(test_acc))

    # 根据参数选择优化器
    if args_optimizer == 'adam':
        optimizer = optim.Adam(filter(lambda p: p.requires_grad, net.parameters()), lr=lr, weight_decay=args.reg)
    elif args_optimizer == 'amsgrad':
        optimizer = optim.Adam(filter(lambda p: p.requires_grad, net.parameters()), lr=lr, weight_decay=args.reg,
                               amsgrad=True)
    elif args_optimizer == 'sgd':
        optimizer = optim.SGD(filter(lambda p: p.requires_grad, net.parameters()), lr=lr, momentum=0.9,
                              weight_decay=args.reg)
    # 定义损失函数
    criterion = nn.CrossEntropyLoss().cuda()

    cnt = 0  # 批次计数器

    # 开始训练循环
    for epoch in range(epochs):
        epoch_loss_collector = []  # 收集每个epoch的损失
        for batch_idx, (x, target) in enumerate(train_dataloader):
            # 将数据移动到GPU
            x, target = x.cuda(), target.cuda()

            # 清零梯度
            optimizer.zero_grad()
            x.requires_grad = False
            target.requires_grad = False
            target = target.long()

            # 前向传播
            _, _, out = net(x)  # 获取网络输出
            loss = criterion(out, target)  # 计算损失

            # 反向传播和参数更新
            loss.backward()
            optimizer.step()

            cnt += 1
            epoch_loss_collector.append(loss.item())

        # 计算并记录平均损失
        epoch_loss = sum(epoch_loss_collector) / len(epoch_loss_collector)
        logger.info('Epoch: %d Loss: %f' % (epoch, epoch_loss))

        # 每10个epoch计算一次准确率
        if epoch % 10 == 0:
            train_acc, _ = compute_accuracy(net, train_dataloader, device=device)
            test_acc, conf_matrix, _ = compute_accuracy(net, test_dataloader, get_confusion_matrix=True, device=device)

    # 训练结束后计算最终准确率
    logger.info('>> Training accuracy: %f' % train_acc)
    logger.info('>> Test accuracy: %f' % test_acc)

    train_acc, _ = compute_accuracy(net, train_dataloader, device=device)
    test_acc, conf_matrix, _ = compute_accuracy(net, test_dataloader, get_confusion_matrix=True, device=device)

    logger.info('>> Training accuracy: %f' % train_acc)
    logger.info('>> Test accuracy: %f' % test_acc)
    net.to('cpu')  # 将网络移回CPU

    logger.info(' ** Training complete **')
    return train_acc, test_acc


def train_net_swim(net_id, net, global_net, previous_nets, train_dataloader, test_dataloader, epochs, lr,
                     args_optimizer, mu, temperature, args,
                     round, device="cpu"):
    """
    SWIM算法的核心训练函数
    实现了基于对比学习的联邦学习训练过程

    Args:
        net_id: 网络ID
        net: 当前客户端的网络
        global_net: 全局网络模型
        previous_nets: 历史网络模型列表
        train_dataloader: 训练数据加载器
        test_dataloader: 测试数据加载器
        epochs: 训练轮数
        lr: 学习率
        args_optimizer: 优化器类型
        mu: SWIM算法的mu参数
        temperature: 对比学习的温度参数
        args: 其他参数
        round: 当前通信轮次
        device: 运行设备

    Returns:
        train_acc: 训练准确率
        test_acc: 测试准确率
    """
    # 使用数据并行包装网络
    net = nn.DataParallel(net)
    net.cuda()
    logger.info('Training network %s' % str(net_id))
    logger.info('n_training: %d' % len(train_dataloader))
    logger.info('n_test: %d' % len(test_dataloader))

    # 计算训练前的准确率
    train_acc, _ = compute_accuracy(net, train_dataloader, device=device)
    test_acc, conf_matrix, _ = compute_accuracy(net, test_dataloader, get_confusion_matrix=True, device=device)

    logger.info('>> Pre-Training Training accuracy: {}'.format(train_acc))
    logger.info('>> Pre-Training Test accuracy: {}'.format(test_acc))

    # 根据参数选择优化器
    if args_optimizer == 'adam':
        optimizer = optim.Adam(filter(lambda p: p.requires_grad, net.parameters()), lr=lr, weight_decay=args.reg)
    elif args_optimizer == 'amsgrad':
        optimizer = optim.Adam(filter(lambda p: p.requires_grad, net.parameters()), lr=lr, weight_decay=args.reg,
                               amsgrad=True)
    elif args_optimizer == 'sgd':
        optimizer = optim.SGD(filter(lambda p: p.requires_grad, net.parameters()), lr=lr, momentum=0.9,
                              weight_decay=args.reg)

    # 定义损失函数
    criterion = nn.CrossEntropyLoss().cuda()

    # 将历史网络移动到GPU
    for previous_net in previous_nets:
        previous_net.cuda()

    cnt = 0  # 批次计数器
    cos = torch.nn.CosineSimilarity(dim=-1)  # 余弦相似度计算

    # 计算历史特征缓存大小
    C = math.ceil(epochs * args.kr)
    Z_prev = [None] * C  # 存储历史特征的缓存
    # SWIM算法的主要训练循环
    for epoch in range(epochs):
        epoch_loss_collector = []      # 总损失收集器
        epoch_loss1_collector = []     # 分类损失收集器
        epoch_loss2_collector = []     # 对比损失收集器

        for batch_idx, (x, target) in enumerate(train_dataloader):
            # 将数据移动到GPU
            x, target = x.cuda(), target.cuda()

            optimizer.zero_grad()
            x.requires_grad = False
            target.requires_grad = False
            target = target.long()

            # 前向传播：获取当前网络的特征和输出
            _, pro1, out = net(x)          # pro1: 当前网络的投影特征
            _, pro2, _ = global_net(x)     # pro2: 全局网络的投影特征

            # 计算正样本相似度（当前网络与全局网络的相似度）
            posi = torch.exp(torch.mean(cos(pro1, pro2).reshape(-1, 1)) / temperature)

            # 初始化负样本相似度（这里实际上是0，因为自己与自己的相似度减去自己与自己的相似度）
            nega = torch.exp(torch.mean(cos(pro1, pro1).reshape(-1, 1)) / temperature) - torch.exp(
                torch.mean(cos(pro1, pro1).reshape(-1, 1)) / temperature)

            # 遍历历史网络，计算对比学习损失
            for previous_net in previous_nets:
                previous_net.cuda()
                _, pro3, _ = previous_net(x)  # pro3: 历史网络的投影特征

                # 将历史特征存储到缓存中（循环缓存）
                Z_prev[cnt % C] = pro3

                # 遍历缓存中的历史特征
                for i in range(0, C):
                    if Z_prev[i] != None:
                        # 计算当前特征与历史特征的相似度
                        t = torch.mean(cos(pro1, Z_prev[i]).reshape(-1, 1))
                        if t >= 0.5:
                            # 如果相似度高，作为正样本
                            posi = posi + torch.exp(t / temperature)
                        else:
                            # 如果相似度低，作为负样本
                            nega = nega + torch.exp(t / temperature)

                # 将历史网络移回CPU以节省GPU内存
                previous_net.to('cpu')

            # 计算两个损失项
            loss1 = criterion(out, target)  # 分类损失
            loss2 = torch.mean((- torch.log(posi / (posi + nega))))  # 对比学习损失

            # 动态调整损失权重（随着训练进行，对比损失权重逐渐减小）
            mu = 0.5 - (round + 1) / (2 * args.comm_round)
            loss = (1 - mu) * loss1 + mu * loss2  # 总损失

            # 反向传播和参数更新
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            # 记录损失
            cnt += 1
            epoch_loss_collector.append(loss.item())
            epoch_loss1_collector.append(loss1.item())
            epoch_loss2_collector.append(loss2.item())
           # epoch_loss2_collector.append(loss1.item())  # 注意：这里可能是代码错误，重复添加了loss1

        # 计算并记录平均损失
        epoch_loss = sum(epoch_loss_collector) / len(epoch_loss_collector)
        epoch_loss1 = sum(epoch_loss1_collector) / len(epoch_loss1_collector)
        epoch_loss2 = sum(epoch_loss2_collector) / len(epoch_loss2_collector)

        logger.info('Epoch: %d Loss: %f Loss1: %f Loss2: %f' % (epoch, epoch_loss, epoch_loss1, epoch_loss2))

    # 训练完成后，将所有历史网络移回CPU
    for previous_net in previous_nets:
        previous_net.to('cpu')

    # 计算最终的训练和测试准确率
    train_acc, _ = compute_accuracy(net, train_dataloader, device=device)
    test_acc, conf_matrix, _ = compute_accuracy(net, test_dataloader, get_confusion_matrix=True, device=device)

    logger.info('>> Training accuracy: %f' % train_acc)
    logger.info('>> Test accuracy: %f' % test_acc)
    net.to('cpu')  # 将当前网络也移回CPU
    logger.info(' ** Training complete **')
    return train_acc, test_acc



def local_train_net(nets, args, net_dataidx_map, train_dl=None, test_dl=None, global_model=None, prev_model_pool=None,
                    server_c=None, clients_c=None, round=None, device="cpu"):
    """
    本地训练网络的主函数
    根据不同的算法（FedAvg、SWIM、本地训练）调用相应的训练函数

    Args:
        nets: 客户端网络字典
        args: 命令行参数
        net_dataidx_map: 网络数据索引映射
        train_dl: 训练数据加载器
        test_dl: 测试数据加载器
        global_model: 全局模型
        prev_model_pool: 历史模型池
        server_c: 服务器参数
        clients_c: 客户端参数
        round: 当前通信轮次
        device: 运行设备

    Returns:
        nets: 训练后的网络字典
    """
    avg_acc = 0.0  # 平均准确率
    acc_list = []  # 准确率列表

    # 如果有全局模型，将其移动到GPU
    if global_model:
        global_model.cuda()

    # 处理服务器参数（如果有的话）
    if server_c:
        server_c.cuda()
        server_c_collector = list(server_c.cuda().parameters())
        new_server_c_collector = copy.deepcopy(server_c_collector)

    # 遍历每个客户端网络进行训练
    for net_id, net in nets.items():
        # 获取当前客户端的数据索引
        dataidxs = net_dataidx_map[net_id]

        logger.info("Training network %s. n_training: %d" % (str(net_id), len(dataidxs)))

        # 为当前客户端创建本地数据加载器
        train_dl_local, test_dl_local, _, _ = get_dataloader(args.dataset, args.datadir, args.batch_size, 32, dataidxs)
        train_dl_global, test_dl_global, _, _ = get_dataloader(args.dataset, args.datadir, args.batch_size, 32)
        n_epoch = args.epochs

        # 根据算法类型选择相应的训练函数
        if args.alg == 'fedavg':
            # 使用标准的FedAvg训练
            trainacc, testacc = train_net(net_id, net, train_dl_local, test_dl, n_epoch, args.lr, args.optimizer, args,
                                          device=device)
        elif args.alg == 'swim':
            # 使用SWIM算法训练
            prev_models = []
            # 从历史模型池中提取当前客户端的历史模型
            for i in range(len(prev_model_pool)):
                prev_models.append(prev_model_pool[i][net_id])
            # 调用SWIM训练函数
            trainacc, testacc = train_net_swim(net_id, net, global_model, prev_models, train_dl_local, test_dl,
                                                 n_epoch, args.lr,
                                                 args.optimizer, args.mu, args.temperature, args, round, device=device)

        elif args.alg == 'local_training':
            # 仅进行本地训练（不进行联邦学习）
            trainacc, testacc = train_net(net_id, net, train_dl_local, test_dl, n_epoch, args.lr, args.optimizer, args,
                                          device=device)

        # 记录训练结果
        logger.info("net %d final test acc %f" % (net_id, testacc))
        avg_acc += testacc
        acc_list.append(testacc)

    # 计算平均准确率
    avg_acc /= args.n_parties
    if args.alg == 'local_training':
        logger.info("avg test acc %f" % avg_acc)
        logger.info("std acc %f" % np.std(acc_list))

    # 清理GPU内存
    if global_model:
        global_model.to('cpu')
    if server_c:
        for param_index, param in enumerate(server_c.parameters()):
            server_c_collector[param_index] = new_server_c_collector[param_index]
        server_c.to('cpu')

    return nets


if __name__ == '__main__':
    """
    主程序入口
    实现SWIM联邦学习算法的完整流程
    """
    # 解析命令行参数
    args = get_args()

    # 创建必要的目录
    mkdirs(args.logdir)
    mkdirs(args.modeldir)

    # 保存实验参数到JSON文件
    if args.log_file_name is None:
        argument_path = 'experiment_arguments-%s.json' % datetime.datetime.now().strftime("%Y-%m-%d-%H%M-%S")
    else:
        argument_path = args.log_file_name + '.json'
    with open(os.path.join(args.logdir, argument_path), 'w') as f:
        json.dump(str(args), f)

    # 设置运行设备
    device = torch.device(args.device)

    # 清除现有的日志处理器
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    # 配置日志系统
    if args.log_file_name is None:
        args.log_file_name = 'experiment_log-%s' % (datetime.datetime.now().strftime("%Y-%m-%d-%H%M-%S"))
    log_path = args.log_file_name + '.log'
    logging.basicConfig(
        filename=os.path.join(args.logdir, log_path),
        format='%(asctime)s %(levelname)-8s %(message)s',
        datefmt='%m-%d %H:%M', level=logging.DEBUG, filemode='w')

    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    logger.info(device)

    # 设置随机种子以确保实验可重复
    seed = args.init_seed
    logger.info("#" * 100)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
    random.seed(seed)

    # 数据分割：将数据分配给不同的客户端
    logger.info("Partitioning data")
    X_train, y_train, X_test, y_test, net_dataidx_map, traindata_cls_counts = partition_data(
        args.dataset, args.datadir, args.logdir, args.partition, args.n_parties, beta=args.beta)

    # 计算每轮参与的客户端数量
    n_party_per_round = int(args.n_parties * args.sample_fraction)
    party_list = [i for i in range(args.n_parties)]
    party_list_rounds = []

    # 为每个通信轮次预先确定参与的客户端
    if n_party_per_round != args.n_parties:
        # 如果不是所有客户端都参与，则随机采样
        for i in range(args.comm_round):
            party_list_rounds.append(random.sample(party_list, n_party_per_round))
    else:
        # 如果所有客户端都参与，则每轮都是完整的客户端列表
        for i in range(args.comm_round):
            party_list_rounds.append(party_list)

    # 获取数据集的类别数
    n_classes = len(np.unique(y_train))

    # 创建全局数据加载器（用于评估全局模型性能）
    train_dl_global, test_dl, train_ds_global, test_ds_global = get_dataloader(args.dataset,
                                                                               args.datadir,
                                                                               args.batch_size,
                                                                               32)

    print("len train_dl_global:", len(train_ds_global))
    train_dl = None
    data_size = len(test_ds_global)

    # 初始化客户端网络
    logger.info("Initializing nets")
    nets, local_model_meta_data, layer_type = init_nets(args.net_config, args.n_parties, args, device='cpu')

    # 初始化全局模型
    global_models, global_model_meta_data, global_layer_type = init_nets(args.net_config, 1, args, device='cpu')
    global_model = global_models[0]
    n_comm_rounds = args.comm_round

    # 如果指定了预训练模型，则加载它
    if args.load_model_file and args.alg != 'plot_visual':
        global_model.load_state_dict(torch.load(args.load_model_file))
        n_comm_rounds -= args.load_model_round

    # 如果使用服务器动量，初始化动量变量
    if args.server_momentum:
        moment_v = copy.deepcopy(global_model.state_dict())
        for key in moment_v:
            moment_v[key] = 0
    # SWIM算法的主要执行流程
    if args.alg == 'swim':
        # 初始化历史模型池
        old_nets_pool = []

        # 如果指定了历史模型池文件，则加载它
        if args.load_pool_file:
            for nets_id in range(args.model_buffer_size):
                old_nets, _, _ = init_nets(args.net_config, args.n_parties, args, device='cpu')
                checkpoint = torch.load(args.load_pool_file)
                for net_id, net in old_nets.items():
                    net.load_state_dict(checkpoint['pool' + str(nets_id) + '_' + 'net' + str(net_id)])
                old_nets_pool.append(old_nets)
        elif args.load_first_net:
            # 如果需要加载第一个网络作为历史网络
            if len(old_nets_pool) < args.model_buffer_size:
                old_nets = copy.deepcopy(nets)
                for _, net in old_nets.items():
                    net.eval()  # 设置为评估模式
                    for param in net.parameters():
                        param.requires_grad = False  # 冻结参数

        # 开始联邦学习的通信轮次循环
        for round in range(n_comm_rounds):
            logger.info("in comm round:" + str(round))
            party_list_this_round = party_list_rounds[round]  # 本轮参与的客户端

            # 设置全局模型为评估模式并冻结参数
            global_model.eval()
            for param in global_model.parameters():
                param.requires_grad = False
            global_w = global_model.state_dict()  # 获取全局模型参数

            # 如果使用服务器动量，保存当前参数
            if args.server_momentum:
                old_w = copy.deepcopy(global_model.state_dict())

            # 选择本轮参与的客户端网络
            nets_this_round = {k: nets[k] for k in party_list_this_round}

            # 将全局模型参数分发给参与的客户端
            for net in nets_this_round.values():
                net.load_state_dict(global_w)

            # 执行本地训练（使用SWIM算法）
            local_train_net(nets_this_round, args, net_dataidx_map, train_dl=train_dl, test_dl=test_dl,
                            global_model=global_model, prev_model_pool=old_nets_pool, round=round, device=device)

            # 计算联邦平均的权重（基于数据量）
            total_data_points = sum([len(net_dataidx_map[r]) for r in party_list_this_round])
            fed_avg_freqs = [len(net_dataidx_map[r]) / total_data_points for r in party_list_this_round]

            # 执行联邦平均聚合
            for net_id, net in enumerate(nets_this_round.values()):
                net_para = net.state_dict()
                if net_id == 0:
                    # 第一个客户端：初始化全局参数
                    for key in net_para:
                        global_w[key] = net_para[key] * fed_avg_freqs[net_id]
                else:
                    # 其他客户端：累加加权参数
                    for key in net_para:
                        global_w[key] += net_para[key] * fed_avg_freqs[net_id]

            # 如果使用服务器动量，应用动量更新
            if args.server_momentum:
                delta_w = copy.deepcopy(global_w)
                for key in delta_w:
                    delta_w[key] = old_w[key] - global_w[key]
                    moment_v[key] = args.server_momentum * moment_v[key] + (1 - args.server_momentum) * delta_w[key]
                    global_w[key] = old_w[key] - moment_v[key]

            # 更新全局模型
            global_model.load_state_dict(global_w)

            # 评估全局模型性能
            logger.info('global n_training: %d' % len(train_dl_global))
            logger.info('global n_test: %d' % len(test_dl))
            global_model.cuda()
            train_acc, train_loss = compute_accuracy(global_model, train_dl_global, device=device)
            test_acc, conf_matrix, _ = compute_accuracy(global_model, test_dl, get_confusion_matrix=True, device=device)
            global_model.to('cpu')
            logger.info('>> Global Model Train accuracy: %f' % train_acc)
            logger.info('>> Global Model Test accuracy: %f' % test_acc)
            logger.info('>> Global Model Train loss: %f' % train_loss)

            # 更新历史模型池
            if len(old_nets_pool) < args.model_buffer_size:
                # 如果模型池未满，直接添加当前模型
                old_nets = copy.deepcopy(nets)
                for _, net in old_nets.items():
                    net.eval()  # 设置为评估模式
                    for param in net.parameters():
                        param.requires_grad = False  # 冻结参数
                old_nets_pool.append(old_nets)
            elif args.pool_option == 'FIFO':
                # 如果模型池已满且使用FIFO策略，移除最旧的模型并添加当前模型
                old_nets = copy.deepcopy(nets)
                for _, net in old_nets.items():
                    net.eval()  # 设置为评估模式
                    for param in net.parameters():
                        param.requires_grad = False  # 冻结参数

                # FIFO队列操作：将所有模型向前移动一位
                for i in range(args.model_buffer_size - 2, -1, -1):
                    old_nets_pool[i] = old_nets_pool[i + 1]
                # 将新模型放在队列末尾
                old_nets_pool[args.model_buffer_size - 1] = old_nets

            # 保存模型（如果需要）
            mkdirs(args.modeldir + 'swim/')
            if args.save_model:
                # 保存全局模型
                torch.save(global_model.state_dict(),
                           args.modeldir + 'swim/global_model_' + args.log_file_name + '.pth')
                # 保存第一个客户端的本地模型
                torch.save(nets[0].state_dict(), args.modeldir + 'swim/localmodel0' + args.log_file_name + '.pth')
                # 保存历史模型池
                for nets_id, old_nets in enumerate(old_nets_pool):
                    torch.save({'pool' + str(nets_id) + '_' + 'net' + str(net_id): net.state_dict() for net_id, net in
                                old_nets.items()},
                               args.modeldir + 'swim/prev_model_pool_' + args.log_file_name + '.pth')


    # FedAvg算法的执行流程
    elif args.alg == 'fedavg':
        # 开始联邦学习的通信轮次循环
        for round in range(n_comm_rounds):
            logger.info("in comm round:" + str(round))
            party_list_this_round = party_list_rounds[round]  # 本轮参与的客户端

            # 获取全局模型参数
            global_w = global_model.state_dict()

            # 如果使用服务器动量，保存当前参数
            if args.server_momentum:
                old_w = copy.deepcopy(global_model.state_dict())

            # 选择本轮参与的客户端网络
            nets_this_round = {k: nets[k] for k in party_list_this_round}

            # 将全局模型参数分发给参与的客户端
            for net in nets_this_round.values():
                net.load_state_dict(global_w)

            # 执行本地训练（标准FedAvg训练）
            local_train_net(nets_this_round, args, net_dataidx_map, train_dl=train_dl, test_dl=test_dl, device=device)

            # 计算联邦平均的权重（基于数据量）
            total_data_points = sum([len(net_dataidx_map[r]) for r in party_list_this_round])
            fed_avg_freqs = [len(net_dataidx_map[r]) / total_data_points for r in party_list_this_round]

            # 执行联邦平均聚合
            for net_id, net in enumerate(nets_this_round.values()):
                net_para = net.state_dict()
                if net_id == 0:
                    # 第一个客户端：初始化全局参数
                    for key in net_para:
                        global_w[key] = net_para[key] * fed_avg_freqs[net_id]
                else:
                    # 其他客户端：累加加权参数
                    for key in net_para:
                        global_w[key] += net_para[key] * fed_avg_freqs[net_id]

            # 如果使用服务器动量，应用动量更新
            if args.server_momentum:
                delta_w = copy.deepcopy(global_w)
                for key in delta_w:
                    delta_w[key] = old_w[key] - global_w[key]
                    moment_v[key] = args.server_momentum * moment_v[key] + (1 - args.server_momentum) * delta_w[key]
                    global_w[key] = old_w[key] - moment_v[key]

            # 更新全局模型
            global_model.load_state_dict(global_w)

            # 评估全局模型性能
            logger.info('global n_test: %d' % len(test_dl))
            global_model.cuda()
            train_acc, train_loss = compute_accuracy(global_model, train_dl_global, device=device)
            test_acc, conf_matrix, _ = compute_accuracy(global_model, test_dl, get_confusion_matrix=True, device=device)

            logger.info('>> Global Model Train accuracy: %f' % train_acc)
            logger.info('>> Global Model Test accuracy: %f' % test_acc)
            logger.info('>> Global Model Train loss: %f' % train_loss)

            # 保存模型
            mkdirs(args.modeldir + 'fedavg/')
            global_model.to('cpu')

            torch.save(global_model.state_dict(),
                       args.modeldir + 'fedavg/' + 'globalmodel' + args.log_file_name + '.pth')
            torch.save(nets[0].state_dict(), args.modeldir + 'fedavg/' + 'localmodel0' + args.log_file_name + '.pth')
    # FedProx算法的执行流程
    elif args.alg == 'fedprox':
        # 开始联邦学习的通信轮次循环
        for round in range(n_comm_rounds):
            logger.info("in comm round:" + str(round))
            party_list_this_round = party_list_rounds[round]  # 本轮参与的客户端

            # 获取全局模型参数
            global_w = global_model.state_dict()

            # 选择本轮参与的客户端网络
            nets_this_round = {k: nets[k] for k in party_list_this_round}

            # 将全局模型参数分发给参与的客户端
            for net in nets_this_round.values():
                net.load_state_dict(global_w)

            # 执行本地训练（FedProx训练，包含近端项）
            local_train_net(nets_this_round, args, net_dataidx_map, train_dl=train_dl, test_dl=test_dl,
                            global_model=global_model, device=device)
            global_model.to('cpu')

            # 更新全局模型（联邦平均）
            total_data_points = sum([len(net_dataidx_map[r]) for r in party_list_this_round])
            fed_avg_freqs = [len(net_dataidx_map[r]) / total_data_points for r in party_list_this_round]

            # 执行联邦平均聚合
            for net_id, net in enumerate(nets_this_round.values()):
                net_para = net.state_dict()
                if net_id == 0:
                    # 第一个客户端：初始化全局参数
                    for key in net_para:
                        global_w[key] = net_para[key] * fed_avg_freqs[net_id]
                else:
                    # 其他客户端：累加加权参数
                    for key in net_para:
                        global_w[key] += net_para[key] * fed_avg_freqs[net_id]
            global_model.load_state_dict(global_w)

            # 评估全局模型性能
            logger.info('global n_training: %d' % len(train_dl_global))
            logger.info('global n_test: %d' % len(test_dl))

            global_model.cuda()
            train_acc, train_loss = compute_accuracy(global_model, train_dl_global, device=device)
            test_acc, conf_matrix, _ = compute_accuracy(global_model, test_dl, get_confusion_matrix=True, device=device)

            logger.info('>> Global Model Train accuracy: %f' % train_acc)
            logger.info('>> Global Model Test accuracy: %f' % test_acc)
            logger.info('>> Global Model Train loss: %f' % train_loss)

            # 保存模型
            mkdirs(args.modeldir + 'fedprox/')
            global_model.to('cpu')
            torch.save(global_model.state_dict(), args.modeldir + 'fedprox/' + args.log_file_name + '.pth')

    # 仅本地训练（不进行联邦学习）
    elif args.alg == 'local_training':
        logger.info("Initializing nets")
        # 每个客户端独立训练，不进行模型聚合
        local_train_net(nets, args, net_dataidx_map, train_dl=train_dl, test_dl=test_dl, device=device)

        # 保存所有本地模型
        mkdirs(args.modeldir + 'localmodel/')
        for net_id, net in nets.items():
            torch.save(net.state_dict(),
                       args.modeldir + 'localmodel/' + 'model' + str(net_id) + args.log_file_name + '.pth')

    # 集中式训练（所有数据在一起训练）
    elif args.alg == 'all_in':
        # 创建单个网络用于集中式训练
        nets, _, _ = init_nets(args.net_config, 1, args, device='cpu')

        # 使用全局数据进行训练
        trainacc, testacc = train_net(0, nets[0], train_dl_global, test_dl, args.epochs, args.lr,
                                      args.optimizer, args, device=device)
        logger.info("All in test acc: %f" % testacc)

        # 保存集中式训练的模型
        mkdirs(args.modeldir + 'all_in/')
        torch.save(nets[0].state_dict(), args.modeldir + 'all_in/' + args.log_file_name + '.pth')

