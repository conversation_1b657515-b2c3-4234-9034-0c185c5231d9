# 改进版联邦学习方案：所有客户端训练，只有10%参与聚合，其中一部分是延迟客户端
import os
import argparse
import random
import numpy as np
import torch
import torch.nn.functional as F
from torch import optim
from Models import Mnist_2NN, Mnist_CNN, Cifar_CNN, CNNCifar
from clients import ClientsGroup, client

parser = argparse.ArgumentParser(formatter_class=argparse.ArgumentDefaultsHelpFormatter, description="FedAvg")
parser.add_argument('-g', '--gpu', type=str, default='0', help='gpu id to use(e.g. 0,1,2,3)')
parser.add_argument('-nc', '--num_of_clients', type=int, default=100, help='numer of the clients')
parser.add_argument('-cf', '--cfraction', type=float, default=0.1,
                    help='C fraction for aggregation, 0.1 means 10% clients participate in aggregation')
parser.add_argument('-E', '--epoch', type=int, default=10, help='local train epoch')
parser.add_argument('-B', '--batchsize', type=int, default=50, help='local train batch size')
parser.add_argument('-mn', '--model_name', type=str, default='CNNCifar', help='the model to train')
parser.add_argument('-lr', '--learning_rate', type=float, default=0.01, help='learning rate')
parser.add_argument('-vf', '--val_freq', type=int, default=5, help='model validation frequency(of communications)')
parser.add_argument('-sf', '--save_freq', type=int, default=20, help='model save frequency(of communications)')
parser.add_argument('-ncomm', '--num_comm', type=int, default=1000, help='number of communications')
parser.add_argument('-sp', '--save_path', type=str, default='./checkpoints', help='the saving path of checkpoints')
parser.add_argument('-iid', '--IID', type=int, default=0, help='the way to allocate data to clients')
parser.add_argument('-mu', '--mu', type=float, default=0.01, help='mu parameter for FedProx')

def tes_mkdir(path):
    if not os.path.isdir(path):
        os.mkdir(path)

def select_aggregation_clients(num_clients, cfraction):
    """选择参与聚合的客户端（10%）"""
    num_selected = int(max(num_clients * cfraction, 1))
    selected_indices = np.random.choice(num_clients, num_selected, replace=False)
    return selected_indices.tolist()

def introduce_delays(selected_clients, delay_ratio=0.4):
    """在选中的客户端中引入延迟"""
    num_delayed = int(len(selected_clients) * delay_ratio)
    if num_delayed > 0:
        delayed_indices = np.random.choice(len(selected_clients), num_delayed, replace=False)
        delayed_clients = [selected_clients[i] for i in delayed_indices]
        normal_clients = [selected_clients[i] for i in range(len(selected_clients)) if i not in delayed_indices]
        return normal_clients, delayed_clients
    else:
        return selected_clients, []

def update_delay_status(delay_status, time_stamps, current_round, delayed_clients, max_delay=4):
    """更新延迟状态"""
    # 为延迟客户端设置随机延迟轮数
    for client_id in delayed_clients:
        if delay_status[client_id] == 0:  # 如果之前不是延迟客户端
            delay_status[client_id] = np.random.randint(1, max_delay + 1)  # 随机延迟1-4轮
            time_stamps[client_id] = current_round - delay_status[client_id]
    
    return delay_status, time_stamps

if __name__ == "__main__":
    args = parser.parse_args()
    args = args.__dict__

    tes_mkdir(args['save_path'])

    os.environ['CUDA_VISIBLE_DEVICES'] = args['gpu']
    dev = torch.device("cuda") if torch.cuda.is_available() else torch.device("cpu")

    # 初始化模型
    net = None
    if args['model_name'] == 'mnist_2nn':
        net = Mnist_2NN()
    elif args['model_name'] == 'mnist_cnn':
        net = Mnist_CNN()
    elif args['model_name'] == 'cifar10_cnn':
        net = Cifar_CNN()
    elif args['model_name'] == 'CNNCifar':
        net = CNNCifar()

    if torch.cuda.device_count() > 1:
        print("Let's use", torch.cuda.device_count(), "GPUs!")
        net = torch.nn.DataParallel(net)
    net = net.to(dev)

    loss_func = F.cross_entropy

    # 初始化客户端
    myClients = ClientsGroup('cifar10', args['IID'], args['num_of_clients'], dev, args["mu"])
    testDataLoader = myClients.test_data_loader

    # 初始化全局模型参数
    global_parameters = {}
    for key, var in net.state_dict().items():
        global_parameters[key] = var.clone()

    # 联邦学习相关参数设置
    client_nums = args['num_of_clients']
    num_aggregation_clients = int(max(client_nums * args['cfraction'], 1))  # 参与聚合的客户端数量
    delay_ratio = 0.4  # 延迟客户端在选中客户端中的比例
    stale_threshold = 6  # 陈旧模型聚合阈值
    max_delay_rounds = 4  # 最大延迟轮数
    
    # 客户端状态跟踪
    delay_status = [0] * client_nums  # 0表示正常，>0表示延迟轮数
    time_stamps = [0] * client_nums   # 客户端最后参与训练的轮次
    client_weights = [1.0] * client_nums  # 客户端权重
    
    # 存储所有客户端的本地模型（用于延迟客户端）
    all_client_models = {}
    for i in range(client_nums):
        all_client_models[f'client{i}'] = None
    
    Accuracy = []  # 存放模型准确率

    print(f"开始联邦学习训练...")
    print(f"总客户端数: {client_nums}")
    print(f"每轮参与聚合的客户端数: {num_aggregation_clients}")
    print(f"延迟客户端比例: {delay_ratio}")
    print(f"陈旧模型聚合阈值: {stale_threshold}")
    print("="*50)

    for round_idx in range(args['num_comm']):
        print(f"\n=== 通信轮次 {round_idx + 1} ===")

        # 第一阶段：所有客户端进行本地训练
        print("第一阶段：所有客户端进行本地训练...")
        for client_id in range(client_nums):
            opti = optim.SGD(net.parameters(), lr=args['learning_rate'])
            local_model = myClients.clients_set[f'client{client_id}'].localUpdate(
                args['epoch'], args['batchsize'], net, loss_func, opti, global_parameters, args["mu"]
            )
            all_client_models[f'client{client_id}'] = local_model

        print(f"所有 {client_nums} 个客户端完成本地训练")

        # 第二阶段：选择参与聚合的客户端（10%）
        selected_clients = select_aggregation_clients(client_nums, args['cfraction'])
        print(f"选择参与聚合的客户端: {selected_clients}")

        # 第三阶段：在选中的客户端中引入延迟
        normal_clients, delayed_clients = introduce_delays(selected_clients, delay_ratio)
        print(f"正常客户端: {normal_clients}")
        print(f"延迟客户端: {delayed_clients}")

        # 更新延迟状态
        delay_status, time_stamps = update_delay_status(
            delay_status, time_stamps, round_idx, delayed_clients, max_delay_rounds
        )

        # 第四阶段：确定实际参与聚合的客户端
        aggregation_clients = []
        aggregation_weights = []

        # 添加正常客户端
        for client_id in normal_clients:
            aggregation_clients.append(client_id)
            aggregation_weights.append(1.0)
            time_stamps[client_id] = round_idx + 1

        # 处理延迟客户端
        for client_id in delayed_clients:
            staleness = (round_idx + 1) - time_stamps[client_id]
            if staleness <= stale_threshold:
                aggregation_clients.append(client_id)
                # 基于陈旧程度设置权重
                weight = 2 ** (-staleness)
                aggregation_weights.append(weight)
                print(f"延迟客户端 {client_id} 参与聚合，陈旧程度: {staleness}, 权重: {weight:.4f}")
            else:
                print(f"延迟客户端 {client_id} 陈旧程度过高 ({staleness} > {stale_threshold})，不参与聚合")

        print(f"实际参与聚合的客户端: {aggregation_clients}")
        print(f"对应权重: {[f'{w:.4f}' for w in aggregation_weights]}")

        # 第五阶段：模型聚合
        if len(aggregation_clients) > 0:
            sum_parameters = None
            total_weight = sum(aggregation_weights)

            for i, client_id in enumerate(aggregation_clients):
                local_parameters = all_client_models[f'client{client_id}']
                weight = aggregation_weights[i] / total_weight

                if sum_parameters is None:
                    sum_parameters = {}
                    for key, var in local_parameters.items():
                        sum_parameters[key] = var.clone() * weight
                else:
                    for key, var in local_parameters.items():
                        sum_parameters[key] += var * weight

            # 更新全局模型
            global_parameters = sum_parameters
            print(f"模型聚合完成，使用了 {len(aggregation_clients)} 个客户端的模型")
        else:
            print("警告：没有客户端参与聚合，保持全局模型不变")

        # 第六阶段：模型评估
        with torch.no_grad():
            if (round_idx + 1) % args['val_freq'] == 0:
                net.load_state_dict(global_parameters, strict=True)
                sum_accu = 0
                num = 0
                for data, label in testDataLoader:
                    data, label = data.to(dev), label.to(dev)
                    preds = net(data)
                    preds = torch.argmax(preds, dim=1)
                    sum_accu += (preds == label).float().mean()
                    num += 1
                accuracy = (sum_accu / num).cpu().item()
                print(f'轮次 {round_idx + 1} 准确率: {accuracy:.4f}')
                Accuracy.append(accuracy)

        # 保存模型
        if (round_idx + 1) % args['save_freq'] == 0:
            torch.save(net.state_dict(), os.path.join(args['save_path'],
                                                      f'{args["model_name"]}_num_comm{round_idx}'))

    # 保存结果
    result_dir = os.path.join(os.getcwd(), "results", "AllTrain_10Percent_Delay")
    os.makedirs(result_dir, exist_ok=True)
    filename = f'Accuracy_AllTrain_10Percent_Delay_mu{args["mu"]}_lr{args["learning_rate"]}_E{args["epoch"]}.txt'
    np.savetxt(os.path.join(result_dir, filename), Accuracy)

    print(f"\n训练完成！")
    print(f"最终准确率: {Accuracy[-1]:.4f}")
    print(f"结果已保存到: {os.path.join(result_dir, filename)}")
