#!/usr/bin/env python3
# 清理损坏的模型文件并重新开始训练
import os
import shutil
import glob

def clean_checkpoints():
    """清理checkpoints目录"""
    checkpoint_dir = "./checkpoints"
    
    if os.path.exists(checkpoint_dir):
        print(f"清理 {checkpoint_dir} 目录...")
        # 删除所有模型文件
        model_files = glob.glob(os.path.join(checkpoint_dir, "*"))
        for file_path in model_files:
            try:
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    print(f"删除文件: {file_path}")
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                    print(f"删除目录: {file_path}")
            except Exception as e:
                print(f"删除失败 {file_path}: {e}")
        print("checkpoints目录清理完成")
    else:
        print("checkpoints目录不存在，创建新目录...")
        os.makedirs(checkpoint_dir)

def clean_results():
    """清理结果目录"""
    result_dirs = ["./nonIID", "./results"]
    
    for result_dir in result_dirs:
        if os.path.exists(result_dir):
            print(f"清理 {result_dir} 目录...")
            try:
                shutil.rmtree(result_dir)
                print(f"{result_dir} 目录清理完成")
            except Exception as e:
                print(f"清理失败 {result_dir}: {e}")

def check_learning_rates():
    """检查学习率设置"""
    print("\n学习率建议:")
    print("  正常客户端学习率: 0.01 - 0.1")
    print("  延迟客户端学习率: 0.01 - 0.02")
    print("  避免使用过高的学习率 (>0.1) 以防止梯度爆炸")

def main():
    print("="*60)
    print("清理和重启联邦学习训练")
    print("="*60)
    
    # 清理checkpoints
    clean_checkpoints()
    
    # 询问是否清理结果
    response = input("\n是否也清理结果目录? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        clean_results()
    
    # 显示学习率建议
    check_learning_rates()
    
    print("\n="*60)
    print("清理完成！现在可以重新开始训练:")
    print("  python main.py -lr 0.01 -dlr 0.02")
    print("或者:")
    print("  python main.py -lr 0.05 -dlr 0.02")
    print("="*60)

if __name__ == "__main__":
    main()
